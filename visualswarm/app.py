# -*- coding: utf-8 -*-
"""
VisualSwarm 主应用程序
======================

这是VisualSwarm系统的主应用程序，负责启动和协调所有子系统。
包括视觉处理、行为计算、电机控制等多个进程的管理。

主要功能:
- 启动多进程架构的集群系统
- 协调视觉感知、行为决策和运动控制
- 提供健康检查和系统监控
- 支持仿真和真实机器人两种模式

作者: mezdahun
描述: VisualSwarm的主应用程序
"""

from multiprocessing import Process, Queue
import sys
import signal
import time
import json

import visualswarm.contrib.vision
from visualswarm import env
from visualswarm.monitoring import ifdb, drive_uploader, web_vision  # system_monitor
from visualswarm.vision import vacquire, vprocess
from visualswarm.contrib import logparams, vision, simulation, monitoring
from visualswarm.contrib import behavior as behav_cont
from visualswarm.behavior import behavior
from visualswarm.control import motorinterface, motoroutput

# 如果不是仿真模式，初始化D-Bus主循环（用于Thymio通信）
if not simulation.ENABLE_SIMULATION:
    import dbus.mainloop.glib
    dbus.mainloop.glib.threads_init()

# 设置信号处理器，用于优雅地处理中断信号
signal.signal(signal.SIGINT, signal.default_int_handler)

# 设置日志系统
import os
ROBOT_NAME = os.getenv('ROBOT_NAME', 'Robot')  # 从环境变量获取机器人名称

# 如果启用云日志，配置Google Cloud Logging
if monitoring.ENABLE_CLOUD_LOGGING:
    import google.cloud.logging
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = monitoring.GOOGLE_APPLICATION_CREDENTIALS
    # 实例化客户端
    client = google.cloud.logging.Client()
    client.get_default_handler()
    client.setup_logging()

# 配置本地日志
import logging
logging.basicConfig()
logger = logging.getLogger(f'VSWRM|{ROBOT_NAME}')  # 创建带机器人名称的日志器
logger.setLevel(monitoring.LOG_LEVEL)
bcolors = logparams.BColors  # 终端颜色配置


def health():
    """
    健康检查入口点

    用于检查VisualSwarm应用程序是否正常运行。
    这是一个简单的健康检查函数，可以通过命令行调用。
    """
    logger.info("VisualSwarm application OK!")


def start_application(with_control=False):
    """
    启动VisualSwarm应用程序

    参数:
        with_control (bool): 是否启用电机控制，默认为False（仅视觉模式）

    功能:
        - 初始化数据库连接
        - 创建进程间通信队列
        - 启动视觉处理、行为计算、电机控制等进程
        - 协调整个系统的运行
    """
    # 如果请求，启动时创建新的数据库
    if env.INFLUX_FRESH_DB_UPON_START:
        logger.info(f'{bcolors.OKGREEN}CLEAN InfluxDB{bcolors.ENDC} upon start as requested')
        ifclient = ifdb.create_ifclient()
        ifclient.drop_database(env.INFLUX_DB_NAME)
        ifclient.create_database(env.INFLUX_DB_NAME)

    logger.info(f'{bcolors.OKGREEN}START vision stream{bcolors.ENDC} ')

    # 如果启用控制，连接到Thymio机器人
    if with_control:
        motorinterface.asebamedulla_init()

    # 创建进程间通信队列
    raw_vision_stream = Queue()          # 原始视觉数据流
    high_level_vision_stream = Queue()   # 高级视觉处理数据流

    # 根据配置决定是否创建可视化流
    if vision.SHOW_VISION_STREAMS or monitoring.SAVE_VISION_VIDEO:
        # 显示原始和处理后的摄像头流
        visualization_stream = Queue()
    else:
        visualization_stream = None

    # 如果启用交互式颜色调整
    if vision.FIND_COLOR_INTERACTIVE:
        # 交互式目标参数调整已开启
        target_config_stream = Queue()
        # 覆盖可视化设置，否则交互式参数调整没有意义
        visualization_stream = Queue()
    else:
        target_config_stream = None

    # 创建其他数据流队列
    VPF_stream = Queue()                 # 视觉投影场数据流
    control_stream = Queue()             # 控制命令数据流
    motor_control_mode_stream = Queue()  # 电机控制模式数据流
    emergency_stream = Queue()           # 紧急情况数据流

    # 创建主要进程
    # 原始视觉获取进程
    raw_vision = Process(target=vacquire.raw_vision, args=(raw_vision_stream,))

    # 根据识别类型选择高级视觉处理目标函数
    if vision.RECOGNITION_TYPE=="Color":
        vp_target = vprocess.high_level_vision          # 颜色识别
    elif vision.RECOGNITION_TYPE=="CNN":
        vp_target = vprocess.high_level_vision_CNN_calib # CNN识别（带校准）

    # 创建高级视觉处理进程池
    high_level_vision_pool = [Process(target=vp_target,
                                      args=(raw_vision_stream,
                                            high_level_vision_stream,
                                            visualization_stream,
                                            target_config_stream,)) for i in range(
        visualswarm.contrib.vision.NUM_SEGMENTATION_PROCS)]  # 根据分割进程数创建进程池

    # 创建其他关键进程
    # visualizer = Process(target=vprocess.visualizer, args=(visualization_stream, target_config_stream,))
    visualizer = Process(target=web_vision.web_vision_process, args=(visualization_stream,))  # Web可视化进程
    VPF_extractor = Process(target=vprocess.VPF_extraction, args=(high_level_vision_stream, VPF_stream,))  # VPF提取进程
    behavior_proc = Process(target=behavior.VPF_to_behavior, args=(VPF_stream, control_stream,
                                                                   motor_control_mode_stream, with_control))  # 行为计算进程
    motor_control = Process(target=motoroutput.control_thymio, args=(control_stream, motor_control_mode_stream,
                                                                     emergency_stream, with_control))  # 电机控制进程
    # system_monitor_proc = Process(target=system_monitor.system_monitor)  # 系统监控进程（已注释）
    emergency_proc = Process(target=motoroutput.emergency_behavior, args=(emergency_stream,))  # 紧急行为进程

    try:
        # 启动子进程
        if vision.RECOGNITION_TYPE == "Color":
            logger.info(f'{bcolors.OKGREEN}START{bcolors.ENDC} raw vision in separate process')
            raw_vision.start()  # 仅在颜色识别模式下启动原始视觉进程

        logger.info(f'{bcolors.OKGREEN}START{bcolors.ENDC} high level vision processes')
        # 启动高级视觉处理进程池
        for proc in high_level_vision_pool:
            proc.start()
            time.sleep(0.5)  # 进程间启动延迟，避免资源竞争

        # 启动其他核心进程
        visualizer.start()      # 启动可视化进程
        VPF_extractor.start()   # 启动VPF提取进程
        behavior_proc.start()   # 启动行为计算进程
        motor_control.start()   # 启动电机控制进程
        # system_monitor_proc.start()  # 启动系统监控进程（已注释）

        # 如果启用控制，启动紧急行为进程
        if with_control:
            emergency_proc.start()

        # 在主进程中等待子进程终止
        visualizer.join()       # 等待可视化进程结束
        for proc in high_level_vision_pool:
            proc.join()         # 等待所有高级视觉处理进程结束
        if vision.RECOGNITION_TYPE == "Color":
            raw_vision.join()   # 等待原始视觉进程结束（仅颜色模式）
        VPF_extractor.join()    # 等待VPF提取进程结束
        behavior_proc.join()    # 等待行为计算进程结束
        motor_control.join()    # 等待电机控制进程结束
        # system_monitor_proc.join()  # 等待系统监控进程结束（已注释）
        if with_control:
            emergency_proc.join()  # 等待紧急行为进程结束

    except KeyboardInterrupt:
        # 在优雅退出期间抑制来自交错队列的所有错误消息
        sys.stderr = object

        logger.info(f'{bcolors.WARNING}EXIT gracefully on KeyboardInterrupt{bcolors.ENDC}')

        # 终止进程（按依赖关系逆序终止）
        if with_control:
            emergency_proc.terminate()
            emergency_proc.join()
            logger.info(f'{bcolors.WARNING}TERMINATED{bcolors.ENDC} emergency process and joined!')

        # system_monitor_proc.terminate()
        # system_monitor_proc.join()
        logger.info(f'{bcolors.WARNING}TERMINATED{bcolors.ENDC} system monitor process and joined!')

        motor_control.terminate()
        motor_control.join()
        logger.info(f'{bcolors.WARNING}TERMINATED{bcolors.ENDC} motor control process and joined!')

        behavior_proc.terminate()
        behavior_proc.join()
        logger.info(f'{bcolors.WARNING}TERMINATED{bcolors.ENDC} control parameter calculations!')

        VPF_extractor.terminate()
        VPF_extractor.join()
        logger.info(f'{bcolors.WARNING}TERMINATED{bcolors.ENDC} visual field segmentation!')

        visualizer.terminate()
        visualizer.join()
        logger.info(f'{bcolors.WARNING}TERMINATED{bcolors.ENDC} visualization stream!')

        # 终止高级视觉处理进程池
        for proc in high_level_vision_pool:
            proc.terminate()
            proc.join()
        logger.info(f'{bcolors.WARNING}TERMINATED{bcolors.ENDC} high level vision process(es) and joined!')

        # 如果是颜色识别模式，终止原始视觉进程
        if vision.RECOGNITION_TYPE == "Color":
            raw_vision.terminate()
            raw_vision.join()
            logger.info(f'{bcolors.WARNING}TERMINATED{bcolors.ENDC} Raw vision process and joined!')

        # 关闭队列
        raw_vision_stream.close()
        high_level_vision_stream.close()
        VPF_stream.close()
        logger.info(f'{bcolors.WARNING}CLOSED{bcolors.ENDC} vision streams!')

        if visualization_stream is not None:
            visualization_stream.close()
            logger.info(f'{bcolors.WARNING}CLOSED{bcolors.ENDC} visualization stream!')

        if target_config_stream is not None:
            target_config_stream.close()
            logger.info(f'{bcolors.WARNING}CLOSED{bcolors.ENDC} configuration stream!')

        control_stream.close()
        logger.info(f'{bcolors.WARNING}CLOSED{bcolors.ENDC} control parameter stream!')

        motor_control_mode_stream.close()
        logger.info(f'{bcolors.WARNING}CLOSED{bcolors.ENDC} movement mode stream!')

        emergency_stream.close()
        logger.info(f'{bcolors.WARNING}CLOSED{bcolors.ENDC} emergency stream!')

        # 如果启用控制，安全停止Thymio机器人
        if with_control:
            logger.info(f'{bcolors.OKGREEN}Setting Thymio2 velocity to zero...{bcolors.ENDC}')
            dbus.mainloop.glib.DBusGMainLoop(set_as_default=True)
            bus = dbus.SessionBus()
            network = dbus.Interface(bus.get_object('ch.epfl.mobots.Aseba', '/'),
                                     dbus_interface='ch.epfl.mobots.AsebaNetwork')
            # 将Thymio机器人的左右电机速度设置为0
            network.SetVariable("thymio-II", "motor.left.target", [0])
            network.SetVariable("thymio-II", "motor.right.target", [0])
            # 关闭LED灯
            motoroutput.light_up_led(network, 0, 0, 0)
            # 结束Aseba连接
            motorinterface.asebamedulla_end()

        # 如果启用云存储，上传生成的数据
        if monitoring.ENABLE_CLOUD_STORAGE:
            logger.info(f'{bcolors.OKGREEN}UPLOAD{bcolors.ENDC} generated videos to Google Drive...')
            # 上传视觉视频
            drive_uploader.upload_vision_videos(monitoring.SAVED_VIDEO_FOLDER)
            # 上传状态变量数据
            drive_uploader.upload_statevars(monitoring.SAVED_VIDEO_FOLDER)

            # 如果保存了CNN训练数据，也上传这些数据
            if monitoring.SAVE_CNN_TRAINING_DATA:
                training_data_folder = os.path.join(monitoring.SAVED_VIDEO_FOLDER, 'training_data')
                params_fpath = os.path.join(training_data_folder,'params.json')
                with open(params_fpath, 'w') as param_f:
                    logger.info('Saving experiment behavior parameters...')
                    # 保存实验行为参数到JSON文件
                    json.dump(behav_cont.get_params(), param_f, indent=4)
                # 压缩并上传CNN训练数据
                drive_uploader.zipupload_CNN_training_data(training_data_folder)

        logger.info(f'{bcolors.OKGREEN}EXITED Gracefully. Bye bye!{bcolors.ENDC}')


def start_application_with_control():
    """
    启动带电机控制的VisualSwarm应用程序

    这是start_application(with_control=True)的便捷包装函数，
    用于启动完整的集群机器人系统，包括视觉处理和电机控制。
    """
    start_application(with_control=True)

