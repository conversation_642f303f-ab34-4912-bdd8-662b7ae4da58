# -*- coding: utf-8 -*-
"""
摄像头模块参数
==============

该模块包含摄像头相关的参数，如分辨率、翻转和摄像头模式，
用于获取原始图像流。

作者: mezdahun
描述: 摄像头模块相关参数，如分辨率、翻转和摄像头模式，用于获取原始图像流
"""
import os

# 基础参数
RESOLUTION = (int(os.getenv('RES_WIDTH', '320')), int(os.getenv('RES_HEIGHT', '200')))  # 图像分辨率(宽x高)
print(RESOLUTION)
FRAMERATE = 20                                    # 帧率(fps)
CAPTURE_FORMAT = "bgr"                           # 捕获格式(BGR色彩空间)
USE_VIDEO_PORT = True                            # 使用视频端口
FLIP_CAMERA = bool(int(os.getenv('FLIP_CAMERA', '1')))  # 是否翻转摄像头图像

# 稳定色彩空间
FIX_ISO = False                                  # 是否固定ISO值
ISO = 100                                        # ISO感光度值
AWB_MODE = 'off'                                 # 自动白平衡模式(关闭)
