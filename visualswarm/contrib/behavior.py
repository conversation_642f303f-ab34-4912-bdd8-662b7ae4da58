# -*- coding: utf-8 -*-
"""
集群行为参数模块
================

该模块包含与集群算法相关的参数。这些参数可以通过外部JSON文件传递，
方法是在BEHAVE_PARAMS_JSON_PATH变量中传递文件路径。
如果路径错误会抛出异常。如果没有传递任何内容，则使用这里定义的默认值。

这些参数基于Bastien & Romanczuk (2020)的视觉集群算法：
https://advances.sciencemag.org/content/6/6/eaay0792

作者: mezdahun
描述: 集群算法相关参数
"""
import json
import os

# 如果请求，从JSON文件读取参数
BEHAVE_PARAMS_JSON_PATH = os.getenv('BEHAVE_PARAMS_JSON_PATH')
if BEHAVE_PARAMS_JSON_PATH is not None:
    if os.path.isfile(BEHAVE_PARAMS_JSON_PATH):
        with open(BEHAVE_PARAMS_JSON_PATH) as f:
            behave_params_dict = json.load(f)
    else:
        raise Exception('在"BEHAVE_PARAMS_JSON_PATH"中定义的参数JSON文件未找到!')
else:
    # 否则我们将使用下面定义的默认值或从环境变量单独传递的值
    behave_params_dict = {}

    # 从环境变量读取各个参数
    GAM_ENV = os.getenv('GAM')
    if GAM_ENV is not None:
        behave_params_dict['GAM'] = float(GAM_ENV)

    V0_ENV = os.getenv('V0')
    if V0_ENV is not None:
        behave_params_dict['V0'] = float(V0_ENV)

    ALP0_ENV = os.getenv('ALP0')
    if ALP0_ENV is not None:
        behave_params_dict['ALP0'] = float(ALP0_ENV)

    ALP1_ENV = os.getenv('ALP1')
    if ALP1_ENV is not None:
        behave_params_dict['ALP1'] = float(ALP1_ENV)

    BET0_ENV = os.getenv('BET0')
    if BET0_ENV is not None:
        behave_params_dict['BET0'] = float(BET0_ENV)

    BET1_ENV = os.getenv('BET1')
    if BET1_ENV is not None:
        behave_params_dict['BET1'] = float(BET1_ENV)

    KAP_ENV = os.getenv('KAP')
    if KAP_ENV is not None:
        behave_params_dict['KAP'] = float(KAP_ENV)


# 速度参数
GAM = behave_params_dict.get('GAM', 0.1)      # 速度衰减系数
V0 = behave_params_dict.get('V0', 125)        # 基础速度
ALP0 = behave_params_dict.get('ALP0', 125)    # 速度响应基础增益
ALP1 = behave_params_dict.get('ALP1', 0.00075)  # 速度响应线性增益
ALP2 = behave_params_dict.get('ALP2', 0)      # 速度响应二次增益

# 航向向量参数
BET0 = behave_params_dict.get('BET0', 10)     # 转向响应基础增益
BET1 = behave_params_dict.get('BET1', 0.001)  # 转向响应线性增益
BET2 = behave_params_dict.get('BET2', 0)      # 转向响应二次增益

# 电机缩放启发式参数Kappa
KAP = behave_params_dict.get('KAP', 1)        # 电机输出缩放因子

# 使智能体在单个圆圈中转动
MOVE_IN_CIRCLE = bool(int(float(os.getenv('MOVE_IN_CIRCLE', '0'))))

def get_params():
    """
    获取所有行为参数

    返回:
        dict: 包含所有行为参数的字典
    """
    params = {"GAM": GAM,      # 速度衰减系数
              "V0": V0,        # 基础速度
              "ALP0": ALP0,    # 速度响应基础增益
              "ALP1": ALP1,    # 速度响应线性增益
              "ALP2": ALP2,    # 速度响应二次增益
              "BET0": BET0,    # 转向响应基础增益
              "BET1": BET1,    # 转向响应线性增益
              "BET2": BET2,    # 转向响应二次增益
              "KAP": KAP}      # 电机输出缩放因子
    return params
