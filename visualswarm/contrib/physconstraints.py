# -*- coding: utf-8 -*-
"""
物理约束参数模块
================

该模块包含与尺寸和在物理环境中导航相关的参数。
这些参数用于将电机值转换为实际的物理运动参数。

作者: mezdahun
描述: 与尺寸和在物理环境中导航相关的参数
"""

# 该参数将电机值对(n, -n)转换为度/秒。例如，如果您将电机值设置为50, -50，
# 机器人将以50*ROT_MULTIPLIER度/秒的速度绕质心旋转。
# 该值将取决于物理环境，即地板质量等。
ROT_MULTIPLIER = 0.386

# 该参数将电机值对(n, n)转换为毫米/秒。例如，如果您将电机值设置为50, 50，
# 机器人将以大约50*FWD_MULTIPLIER毫米/秒的速度向前移动。
# 该值将取决于物理环境，即地板质量等。
FWD_MULTIPLIER = 0.36

# 该参数将电机值对(-n, -n)转换为毫米/秒。例如，如果您将电机值设置为-50, -50，
# 机器人将以大约50*BWD_MULTIPLIER毫米/秒的速度向后移动。
# 该值将取决于物理环境，即地板质量等。
BWD_MULTIPLIER = 0.325
