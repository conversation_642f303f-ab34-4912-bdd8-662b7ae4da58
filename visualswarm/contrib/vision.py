# -*- coding: utf-8 -*-
"""
视觉处理参数模块
================

该模块包含与视觉流实时可视化相关的参数。
包括识别类型、颜色分割、CNN配置、鱼眼校正等设置。

作者: mezdahun
描述: 与视觉流实时可视化相关的参数
"""
from cv2 import cvtColor, COLOR_BGR2HSV
import numpy as np
import os

def calculate_reverse_mapping_fn(lens, orig_img_width):
    """
    计算鱼眼校正的离散化反向映射函数

    参数:
        lens: 镜头参数字典
        orig_img_width: 原始图像宽度

    返回:
        lens: 更新后的镜头参数字典
    """
    # 根据镜头参数计算鱼眼校正的离散化反向映射
    lens['h_reverse_mapping'] = np.array(
        [np.round(num) for num in np.maximum(lens['a_nonlin'] * np.square(lens['h_domain_orig']),
                                             lens['a_lin'] * np.ones(orig_img_width) - lens['offset_lin'])])
    lens['new_width'] = np.sum(lens['h_reverse_mapping'])
    return lens


# 识别类型，支持：'Color'（颜色）或'CNN'
RECOGNITION_TYPE = "CNN"

# 交互式颜色调整
FIND_COLOR_INTERACTIVE = False

# 实时可视化
SHOW_VISION_STREAMS = bool(int(os.getenv('SHOW_VISION_STREAMS', '1')))  # 是否显示视觉流
VIS_DOWNSAMPLE_FACTOR = 1  # 可视化下采样因子

# 绘图，RGB颜色
RAW_CONTOUR_COLOR = (0, 0, 255)      # 原始轮廓颜色（红色）
RAW_CONTOUR_WIDTH = 3                # 原始轮廓宽度
CONVEX_CONTOUR_COLOR = (0, 255, 0)   # 凸轮廓颜色（绿色）
CONVEX_CONTOUR_WIDTH = 3             # 凸轮廓宽度

# 颜色空间分割
if RECOGNITION_TYPE == "Color":
    NUM_SEGMENTATION_PROCS = 6       # 颜色识别时的分割进程数
else:
    NUM_SEGMENTATION_PROCS = 1       # CNN识别时的分割进程数

# 目标颜色（普通摄像头）
# TARGET_RGB_COLOR = (207, 207, 0)
# 目标颜色（NoIR摄像头）
TARGET_RGB_COLOR = (52, 74, 245)     # 目标RGB颜色
HSV_HUE_RANGE = 29                   # HSV色调范围
SV_MINIMUM = 112                     # 饱和度和亮度最小值
SV_MAXIMUM = 255                     # 饱和度和亮度最大值
R, G, B = TARGET_RGB_COLOR
TARGET_HSV_COLOR = cvtColor(np.uint8([[[B, G, R]]]), COLOR_BGR2HSV)  # 转换为HSV颜色
HSV_LOW = np.uint8([TARGET_HSV_COLOR[0][0][0] - HSV_HUE_RANGE, SV_MINIMUM, SV_MINIMUM])   # HSV下限
HSV_HIGH = np.uint8([TARGET_HSV_COLOR[0][0][0] + HSV_HUE_RANGE, SV_MAXIMUM, SV_MAXIMUM])  # HSV上限

# VPF预处理
GAUSSIAN_KERNEL_WIDTH = 9  # 高斯核宽度（原为15）
MEDIAN_BLUR_WIDTH = 5      # 中值模糊宽度（原为9）
MIN_BLOB_AREA = 0          # 最小斑点面积

# 视觉投影
FOV = float(os.getenv('ROBOT_FOV', '6.28'))  # 视野角度（弧度）
H_MARGIN = 1  # 垂直边距（原为10）
W_MARGIN = 1  # 水平边距（原为10）
PHI_START = - (FOV / 2)  # 视野起始角度
PHI_END = (FOV / 2)      # 视野结束角度

# CNN路径配置
MODEL_NAME = '/home/<USER>/VisualSwarm/CNNtools/data/tflite_model/edgetpu'  # 模型路径
# 模型名称
GRAPH_NAME = 'FINAL_ssdnet2_edgetpu_fullinteger.tflite'  # 图模型文件名
LABELMAP_NAME = 'labelmap.txt'                           # 标签映射文件名
# 使用张量处理单元
USE_TPU = True      # 是否使用TPU
INTQUANT = True     # 是否使用整数量化

# CNN处理参数
overlap_removal = bool(int(float(os.getenv('OVERLAP_REMOVAL', '0'))))      # 是否移除重叠
overlap_removal_thr = 0.5                                                  # 重叠移除阈值
min_box_width = 5                                                          # 最小边界框宽度
max_num_detections = int(float(os.getenv('MAX_NUM_DETECTIONS', '9')))      # 最大检测数量
min_confidence = float(os.getenv('MIN_CONFIDENCE', '0.32'))                # 最小置信度

# 取N个最大投影
focus_on_N_largest = bool(int(float(os.getenv('FOCUS_N_NEAREST', '0'))))   # 是否聚焦于N个最近的
N_largest = int(float(os.getenv('N_NEAREST', '4')))                        # N个最大值

# 逐个处理斑点
divided_projection_field = bool(int(float(os.getenv('MULTI_RETINA', '0')))) # 是否使用分割投影场

# 鱼眼镜头近似水平校正
# 偏移量：从左右的像素偏移（输入分辨率宽度为320px）
orig_img_width = 320  # 我们应该从contrib.camera获取，但现在可以保持这样

# 镜头1配置
lens1 = {
    'offset_left': 0,        # 左偏移
    'offset_right': 25,      # 右偏移
    'a_nonlin': 1 * np.pi,   # 非线性系数
    'a_lin': 5,              # 线性系数
    'offset_lin': 0,         # 线性偏移
    'h_domain_orig': np.linspace(PHI_START, PHI_END, orig_img_width)  # 原始水平域
}
lens1 = calculate_reverse_mapping_fn(lens1, orig_img_width)

# 镜头2配置
lens2 = {
    'offset_left': 7,        # 左偏移
    'offset_right': 17,      # 右偏移
    'a_nonlin': 1 * np.pi,   # 非线性系数
    'a_lin': 5,              # 线性系数
    'offset_lin': 0,         # 线性偏移
    'h_domain_orig': np.linspace(PHI_START, PHI_END, orig_img_width)  # 原始水平域
}
lens2 = calculate_reverse_mapping_fn(lens2, orig_img_width)

# 镜头3配置
lens3 = {
    'offset_left': 18,       # 左偏移
    'offset_right': 3,       # 右偏移
    'a_nonlin': 1 * np.pi,   # 非线性系数
    'a_lin': 5,              # 线性系数
    'offset_lin': 0,         # 线性偏移
    'h_domain_orig': np.linspace(PHI_START, PHI_END, orig_img_width)  # 原始水平域
}
lens3 = calculate_reverse_mapping_fn(lens3, orig_img_width)

# 镜头4配置
lens4 = {
    'offset_left': 13,       # 左偏移
    'offset_right': 10,      # 右偏移
    'a_nonlin': 1 * np.pi,   # 非线性系数
    'a_lin': 5,              # 线性系数
    'offset_lin': 0,         # 线性偏移
    'h_domain_orig': np.linspace(PHI_START, PHI_END, orig_img_width)  # 原始水平域
}
lens4 = calculate_reverse_mapping_fn(lens4, orig_img_width)


# 将机器人与镜头连接（在硬件元件上标记为Ri和Li）
LENS_CONFIG={
    'Robot1': lens1,  # 机器人1使用镜头1
    'Robot2': lens2,  # 机器人2使用镜头2
    'Robot3': lens3,  # 机器人3使用镜头3
    'Robot4': lens4   # 机器人4使用镜头4
}

# 使用鱼眼校正
USE_VPF_FISHEYE_CORRECTION = False  # 是否使用VPF鱼眼校正
