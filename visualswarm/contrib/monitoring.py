# -*- coding: utf-8 -*-
"""
监控参数模块
============

该模块包含与Grafana监控和InfluxDB配置相关的参数。
包括数据保存、云日志、视频录制、CNN训练数据收集等功能的配置。

作者: mezdahun
描述: 与Grafana监控和InfluxDB配置相关的参数
"""
import os
import logging

# 使用Grafana和InfluxDB保存和可视化投影场的参数
SAVE_PROJECTION_FIELD = False    # 是否保存投影场数据
DOWNGRADING_FACTOR = 10          # 降级因子（数据采样率）

# 关于监控主算法集群参数的参数
SAVE_CONTROL_PARAMS = False      # 是否保存控制参数

# 通过Google Cloud启用云日志
ENABLE_CLOUD_LOGGING = bool(int(os.getenv('ENABLE_CLOUD_LOGGING', '0')))  # 是否启用云日志
GOOGLE_APPLICATION_CREDENTIALS = os.getenv('GOOGLE_APPLICATION_CREDENTIALS', '/home/<USER>/VisualSwarm/GKEY.json')  # Google应用凭据路径
LOG_LEVEL = logging.getLevelName(os.getenv('LOG_LEVEL', 'DEBUG'))  # 日志级别

# 如果请求，将视觉流保存为视频
SAVE_VISION_VIDEO = bool(int(os.getenv('SAVE_VISION_VIDEO', '0')))  # 是否保存视觉视频
SAVED_VIDEO_FOLDER = '/home/<USER>/VisualSwarm/videos'                  # 保存视频的文件夹
DRIVE_SHARED_FOLDER_ID = "1M3_D-bh5r9wFRQh7KIwoqUCPLXjljjVF"      # Google Drive共享文件夹ID

# 在实验期间收集训练数据以微调基于CNN的视觉
SAVE_CNN_TRAINING_DATA = bool(int(os.getenv('SAVE_CNN_TRAINING_DATA', '0')))  # 是否保存CNN训练数据
CNN_TRAINING_DATA_FREQ = 1  # 频率（Hz）

# 将保存的视频上传到Google Drive
ENABLE_CLOUD_STORAGE = bool(int(os.getenv('ENABLE_CLOUD_STORAGE', '1')))  # 是否启用云存储
CLOUD_STORAGE_AUTH_MODE = 'OAuth2'  # 云存储认证模式：'ServiceAccount'（服务账户）或'OAuth2'
