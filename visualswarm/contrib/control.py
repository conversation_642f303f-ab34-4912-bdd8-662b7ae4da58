# -*- coding: utf-8 -*-
"""
电机控制参数模块
================

该模块包含电机控制相关的参数（Thymio连接）。
包括电机接口、探索模式、障碍物检测等参数配置。

作者: mezdahun
描述: 电机控制相关参数（Thymio连接）
"""
import os

# 电机接口
# Thymio可用的串行端口
THYMIO_DEVICE_PORT = "/dev/ttyACM0"

# 电机缩放校正，将电机缩放放入正确的区域
# MOTOR_SCALE_CORRECTION = 25

# 车轮与地面两个接触点之间的距离（米）
B = 0.11

# Thymio2的最大电机速度
MAX_MOTOR_SPEED = 500


# 探索模式
# 探索模式，可能的值：'RandomWalk'（随机游走）, 'Rotation'（旋转）, 'NoExploration'（无探索）
EXP_MOVE_TYPE = os.getenv('EXP_MOVEMENT', 'NoExploration')

# 如果没有输入，在探索前等待一段时间（秒）
WAIT_BEFORE_SWITCH_MOVEMENT = 1

# 状态LED
EXPLORE_STATUS_RGB = (32, 32, 32)    # 探索状态RGB颜色
BEHAVE_STATUS_RGB = (0, 0, 0)        # 行为状态RGB颜色
EMERGENCY_STATUS_RGB = (32, 0, 0)    # 紧急状态RGB颜色


# 随机游走
# 临时平滑随机游走
SMOOTH_RW = bool(int(os.getenv('SMOOTH_RW', '0')))
# 随机游走改变方向的时间步长（秒）
RW_DT = 3.5

# 随机游走探索期间的固定速度
V_EXP_RW = 200

# 随机游走探索期间给定时间步长内可能的绝对角度变化（弧度）
# 如果为零，探索就是直线移动
DPSI_MAX_EXP = 0.2

# 旋转
# 旋转期间的电机速度（每个电机）（旋转速度）
ROT_MOTOR_SPEED = 50

# 旋转方向，可能的值：'Left'（左）, 'Right'（右）, 'Random'（随机）
ROT_DIRECTION = 'Left'


# 障碍物检测
# 如果设置为"Infinite"，机器人将在边界条件下传送
BORDER_CONDITION = os.getenv('BORDER_CONDITIONS', 'Reality')
# 避障转向方向
AVOID_TURN_DIRECTION = 'Various'  # 或 'Various'

# 紧急监控频率（Hz）（最大值为10Hz，Thymio以此频率更新这些值）
EMERGENCY_CHECK_FREQ = 10

# 水平接近传感器上触发障碍物避障的阈值
EMERGENCY_PROX_THRESHOLD = 2000  # 仿真使用3000（在现实中1500约为10cm，2500约为5cm，适用于白色表面）
EMERGENCY_PROX_THRESHOLD_BACK = 2000  # 后方接近传感器阈值

# 障碍物避障期间远离障碍物的转向角度
turn_angle_correction = 30           # 转向角度校正
desired_alignemnt_angle = 10         # 期望对齐角度（仿真使用30）
OBSTACLE_TURN_ANGLE = desired_alignemnt_angle + turn_angle_correction  # 障碍物转向角度

# 钟摆陷阱
# 传感器值阈值，低于此值时左右传感器值被认为是对称的
SYMMETRICITY_THRESHOLD = 500
# 当障碍物对称时，低于此传感器值的中间前方传感器被忽略
UNCONTINOUTY_THRESHOLD = 3000
# 摆脱钟摆陷阱的转向角度
PENDULUM_TRAP_ANGLE = 90
