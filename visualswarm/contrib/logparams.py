# -*- coding: utf-8 -*-
"""
日志参数模块
============

该模块包含日志相关的参数，主要是用于着色日志输出的ANSI转义字符。

作者: mezdahun
描述: 日志相关参数
"""


# ANSI转义字符，用于给日志着色并使其更易理解
class BColors:
    """
    终端颜色类

    提供ANSI转义序列用于在终端中显示彩色文本
    """
    HEADER = '\033[95m'      # 紫色标题
    OKBLUE = '\033[94m'      # 蓝色（正常）
    OKCYAN = '\033[96m'      # 青色（正常）
    OKGREEN = '\033[92m'     # 绿色（成功）
    WARNING = '\033[93m'     # 黄色（警告）
    FAIL = '\033[91m'        # 红色（失败）
    ENDC = '\033[0m'         # 结束颜色
    BOLD = '\033[1m'         # 粗体
    UNDERLINE = '\033[4m'    # 下划线
