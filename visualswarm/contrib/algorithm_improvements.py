# -*- coding: utf-8 -*-
"""
算法改进模块
============

该模块包含了对主要集群算法的实验性改进，这些改进未在论文中使用。
这些参数可以通过环境变量进行配置，用于测试和优化集群行为。

作者: mezdahun
描述: 主算法的实验性改进，未用于发表
"""
import os


##### 探索行为 #####
# 通过原地旋转朝向最后可见线索进行探索
# 开启/关闭通过原地旋转朝向最后可见线索的探索行为
WITH_EXPLORE_ROT = bool(int(os.getenv('WITH_EXPLORE_ROT', '0')))
# 旋转速度（电机单位）
EXPLORE_ROT_SPEED = int(float(os.getenv('EXPLORE_ROT_SPEED', '50')))

# 连续旋转，保持绝对速度但使用固定的dphi
# 仅保持朝向上一时间步dphi的方向
# 开启/关闭通过旋转朝向最后可见线索的探索行为
WITH_EXPLORE_ROT_CONT = bool(int(os.getenv('WITH_EXPLORE_ROT_CONT', '0')))
# 旋转绝对速度（电机单位）
EXPLORE_ROT_SPEED_CONT = int(float(os.getenv('EXPLORE_ROT_SPEED_CONT', '100')))
# 固定旋转角度（弧度，dphi）
EXPLORE_ROT_THETA_CONT = float(os.getenv('EXPLORE_ROT_THETA_CONT', '0.75'))


##### 前后振荡 #####
# 开启/关闭有限后退运动
WITH_LIMITED_BACKWARDS = bool(int(os.getenv('WITH_LIMITED_BACKWARDS', '0')))
# 最大绝对后退速度（电机单位）
MAX_BACKWARDS_SPEED = int(float(os.getenv('MAX_BACKWARDS_SPEED', '35')))
# 开启/关闭最大前进速度限制
WITH_LIMITED_FORWARD = bool(int(os.getenv('WITH_LIMITED_FORWARD', '0')))
# 最大绝对前进速度（电机单位）
MAX_FORWARD_SPEED = int(float(os.getenv('MAX_FORWARD_SPEED', '250')))


##### S型掩码 #####
# 开启/关闭加速响应的S型掩码函数
WITH_SIGMOID_MASK_ACC = bool(int(os.getenv('WITH_SIGMOID_MASK_ACC', '0')))
# 加速响应掩码的S型陡峭度
SIGMOID_MASK_ACC_STEEP = int(float(os.getenv('SIGMOID_MASK_ACC_STEEP', '15')))
# 开启/关闭转向响应的S型掩码函数
WITH_SIGMOID_MASK_TURN = bool(int(os.getenv('WITH_SIGMOID_MASK_TURN', '0')))
# 转向响应掩码的S型陡峭度
SIGMOID_MASK_TURN_STEEP = int(float(os.getenv('SIGMOID_MASK_TURN_STEEP', '15')))


##### 懒惰转向 #####
# 开启/关闭速度阈值以下的原地转向行为
WITH_IMPR_TURNING = bool(int(os.getenv('WITH_IMPR_TURNING', '0')))
# 当只有少于特定数量的斑点可见时（例如只有1个），朝向整体更多激励转向
STAT_TURN_NUM_BLOB_THRES = int(float(os.getenv('STAT_TURN_NUM_BLOB_THRES', '2')))
# 后退时转向速度的固定乘数（电机单位）
STAT_TURN_SPEED_BACK = int(float(os.getenv('STAT_TURN_SPEED_BACK', '200')))
# 中心化速率，即当可见斑点少于STAT_TURN_NUM_BLOB_THRES时，
# 机器人朝向视觉斑点质心转向的速度。这也会乘以速度，所以更快的机器人转向更快
CENTRALIZE_SPEED = float(os.getenv('CENTRALIZE_SPEED', '0.1'))


##### 选择性斑点过滤 #####
# 选择性过滤视觉斑点
# (1) 仅当有超过2个可见斑点时
# (2) 我们计算视网膜上视觉斑点的质心，并将移除候选者
#     设为按距离质心距离排序的列表中的最后N个斑点
# (3) 我们从候选者中移除低于特定大小的斑点
# 开启/关闭选择性斑点过滤
WITH_SELECTIVE_BLOB_FILTERING = bool(int(os.getenv('WITH_SELECTIVE_BLOB_FILTERING', '0')))
# 选择性移除低于此大小的斑点（仅当它们远离其他斑点时）
MIN_BLOB_SIZE = int(float(os.getenv('MIN_BLOB_SIZE', '8')))
# 当按质心距离排序时，最后N个斑点将成为移除候选者
N_BLOB_CANDIDATES = int(float(os.getenv('N_BLOB_CANDIDATES', '2')))