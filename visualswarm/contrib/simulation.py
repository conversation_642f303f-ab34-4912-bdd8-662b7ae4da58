# -*- coding: utf-8 -*-
"""
仿真参数模块
============

该模块包含与WeBots环境中仿真相关的参数。
包括仿真开关、传感器频率、多线程配置、数据保存等设置。

作者: mezdahun
描述: 与WeBots环境中仿真相关的参数
"""

import os
import numpy as np

# 仿真开关，设置为true以将包从WeBots用作控制器包
# 将其作为环境变量传递（在venv中，使用带有附加环境变量的自定义activate.bat）
ENABLE_SIMULATION = bool(int(os.getenv('ENABLE_SIMULATION', '0')))

# 传感器更新频率
UPFREQ_PROX_HORIZONTAL = 10  # 水平接近传感器频率（Hz）

# 在WeBots仿真期间选择使用多线程或多进程模块的开关。
# 多进程模块的优势是可以扩展并分布在例如机器集群上，
# 尽管这尚未实现。如果使用多线程，WeBots会自动限制
# 仿真的可用资源，因此更安全但更慢。默认值是安全模式，使用线程。
SPARE_RESCOURCES = bool(int(os.getenv('SPARE_RESCOURCES', '1')))

# WeBots环境中Thymio的最大电机速度
MAX_WEBOTS_MOTOR_SPEED = 9.53

# 零角度方向，机器人方向将计算为与此向量的差值
# 如果在WeBots中禁用了某个轴，也在这里写nan
WEBOTS_ZERO_ORIENTATION = [1, np.nan, 0]

# 机器人前进轴
WEBOTS_ROBOT_FWD_AXIS = np.array([0, 0, 1])

# 计算哪个坐标将决定方向符号
WEBOTS_ORIENTATION_SIGN_IDX = np.nonzero(WEBOTS_ROBOT_FWD_AXIS[~np.isnan(np.array(WEBOTS_ZERO_ORIENTATION))])[0][0]

# 如果为true，保存仿真数据
WEBOTS_SAVE_SIMULATION_DATA = bool(int(os.getenv('WEBOTS_SAVE_SIMULATION_DATA', '0')))   # 是否保存仿真数据
WEBOTS_SAVE_SIMULATION_VIDEO = bool(int(os.getenv('WEBOTS_SAVE_SIMULATION_VIDEO', '0'))) # 是否保存仿真视频

# 仿真数据保存位置
WEBOTS_SIM_SAVE_FOLDER = os.getenv('WEBOTS_SIM_SAVE_FOLDER')

# 统一限制仿真结束
PAUSE_SIMULATION_AFTER = int(os.getenv('PAUSE_SIMULATION_AFTER', '0'))  # 仿真暂停时间（秒）

# 暂停行为
PAUSE_BEHAVIOR = os.getenv('PAUSE_BEHAVIOR', 'Pause')  # 暂停行为类型

# WeBots性能日志
WEBOTS_LOG_PERFORMANCE = bool(int(os.getenv('WEBOTS_LOG_PERFORMANCE', '0')))  # 是否记录性能日志
