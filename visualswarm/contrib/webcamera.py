# -*- coding: utf-8 -*-
"""
网络摄像头参数模块
==================

该模块包含网络观察摄像头的参数配置。
用于配置用于观察和监控的外部摄像头设备。

作者: mezdahun
描述: 网络观察摄像头参数
"""
import os

# 基础参数
RESOLUTION = (1000, 1000)                                    # 图像分辨率(宽x高)
FRAMERATE = 15                                               # 帧率(fps)
CAPTURE_FORMAT = "bgr"                                       # 捕获格式(BGR色彩空间)
USE_VIDEO_PORT = True                                        # 使用视频端口
FLIP_CAMERA = bool(int(os.getenv('FLIP_CAMERA', '0')))       # 是否翻转摄像头图像

# 稳定色彩空间
FIX_ISO = True                                               # 是否固定ISO值
ISO = 100                                                    # ISO感光度值
AWB_MODE = 'off'                                             # 自动白平衡模式(关闭)
