# -*- coding: utf-8 -*-
"""
VisualSwarm 配置模块
===================

该模块包含了VisualSwarm系统的所有配置参数和常量定义。
这些配置文件控制着系统的各个方面，包括：

- algorithm_improvements.py: 算法改进相关参数
- behavior.py: 集群行为算法参数
- camera.py: 摄像头配置参数
- control.py: 控制系统参数
- logparams.py: 日志和可视化参数
- monitoring.py: 监控和数据记录参数
- physconstraints.py: 物理约束参数
- puppetmaster.py: 主控节点配置
- simulation.py: 仿真环境配置
- vision.py: 视觉处理参数
- webcamera.py: 网络摄像头配置

作者: mezdahun
描述: VisualSwarm系统配置参数集合
"""