# -*- coding: utf-8 -*-
"""
主控节点参数模块
================

该模块包含与主控子模块相关的参数，该子模块通过SSH和Fabric控制机器人群体。

示例机器人命令：
ENABLE_CLOUD_LOGGING=0 ENABLE_CLOUD_STORAGE=0 SAVE_VISION_VIDEO=1 SHOW_VISION_STREAMS=1
FLIP_CAMERA=0 ROBOT_FOV=3.8 LOG_LEVEL=DEBUG SAVE_CNN_TRAINING_DATA=0 ROBOT_NAME=Robot2 vswrm-start-vision

作者: mezdahun
描述: 与主控子模块相关的参数，该子模块通过SSH和Fabric控制机器人群体
"""

# 要仅选择几个机器人，请取消注释
# selected_robots = [3, 8]

# 选择所有机器人
selected_robots = [i+1 for i in range(10)]

# 所有机器人主机IP地址映射
ALL_HOSTS = {'Robot1': '*************',
             'Robot2': '*************',
             'Robot3': '*************',
             'Robot4': '*************',
             'Robot5': '*************',
             'Robot6': '*************',
             'Robot7': '*************',
             'Robot8': '*************',
             'Robot9': '*************',
             'Robot10': '*************'}

# 根据选择的机器人构建主机字典
HOSTS = {}
for rid in selected_robots:
    HOSTS[f"Robot{rid}"] = ALL_HOSTS[f"Robot{rid}"]

# 网络摄像头主机
WEBCAM_HOSTS = {
    'Birdseye Cam': '***************'  # 鸟瞰摄像头
}

# SSH连接参数
UNAME = 'pi'                           # 用户名
INSTALL_DIR = '/home/<USER>/VisualSwarm'   # 安装目录
