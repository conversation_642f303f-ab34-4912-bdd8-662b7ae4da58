# -*- coding: utf-8 -*-
"""
VisualSwarm 主包
================

VisualSwarm是一个基于视觉的集群机器人系统，实现了Bastien & Romanczuk (2020)
发表的最小视觉集群算法。该系统在Raspberry Pi上运行，用于控制Thymio II机器人的运动。

主要模块:
- behavior: 集群行为算法实现
- vision: 视觉处理和目标检测
- control: 电机控制和运动输出
- monitoring: 数据监控和可视化
- simulation_tools: 仿真工具和数据处理
- contrib: 配置参数和常量定义

作者: mezdahun
项目: VisualSwarm - 基于视觉的集群机器人系统
论文: https://advances.sciencemag.org/content/6/6/eaay0792
"""

__version__ = "1.0.0"
__author__ = "mezdahun"
__email__ = "<EMAIL>"