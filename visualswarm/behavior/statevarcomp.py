# -*- coding: utf-8 -*-
"""
VisualSwarm 状态变量计算模块
============================

该子模块实现了论文中定义的主要行为/运动计算。
基于Bastien & Romanczuk (2020)的视觉集群算法数学模型。

论文链接: https://advances.sciencemag.org/content/6/6/eaay0792

主要功能:
- 计算视觉投影场(VPF)的导数
- 实现核心状态变量计算
- 根据VPF计算速度和转向变化
- 应用集群行为的数学模型

作者: mezdahun
描述: 实现论文中定义的主要行为/运动计算的子模块
"""
import logging

import numpy as np
import numpy.typing as npt
from scipy import integrate

from visualswarm.contrib import behavior, monitoring
from visualswarm.contrib import algorithm_improvements as algoimp

# 使用主日志器
# 设置日志
import os

ROBOT_NAME = os.getenv('ROBOT_NAME', 'Robot')
logger = logging.getLogger(f'VSWRM|{ROBOT_NAME}')
logger.setLevel(monitoring.LOG_LEVEL)


def dPhi_V_of(Phi: npt.ArrayLike, V: npt.ArrayLike) -> npt.ArrayLike:
    """
    计算在给定时间点t时VPF相对于Phi视角数组的导数

    这是集群算法的核心数学操作之一，用于检测视野中目标的边缘。

    参数:
        Phi: 视野轴的线性空间numpy数组
        V: 二进制视觉投影场数组

    返回:
        dPhi_V: V相对于Phi的导数数组

    算法说明:
        - 使用循环填充处理边界情况
        - 计算差分来近似导数
        - 处理边缘的非零值
    """
    # 为边界情况进行循环填充
    padV = np.pad(V, (1, 1), 'wrap')
    dPhi_V_raw = np.diff(padV)

    # 如果边缘有非零值，我们希望包含它
    if dPhi_V_raw[0] > 0 and dPhi_V_raw[-1] > 0:
        dPhi_V_raw = dPhi_V_raw[0:-1]
    else:
        dPhi_V_raw = dPhi_V_raw[1:, ...]

    dPhi_V = dPhi_V_raw  # / (Phi[-1] - Phi[-2])
    return dPhi_V

# 未来光流相关函数
# def dt_V_of(dt, joined_V):
#     """计算VPF对所有Phi视角的时间导数"""
#     dt_V = np.diff(joined_V, axis=0, prepend=0) / dt
#     return dt_V


def compute_state_variables(vel_now: float, Phi: npt.ArrayLike, V_now: npt.ArrayLike,
                            t_now=None, V_prev=None, t_prev=None,
                            GAM=None, V0=None,
                            ALP0=None, ALP1=None, ALP2=None,
                            BET0=None, BET1=None, BET2=None):
    """
    根据主算法计算给定智能体的状态变量

    实现论文中的核心数学模型：
    https://advances.sciencemag.org/content/6/6/eaay0792

    参数:
        vel_now (float): 智能体当前速度
        Phi (npt.ArrayLike): 视野轴的线性空间numpy数组
        V_now (npt.ArrayLike): 当前二进制视觉投影场数组
        t_now: 当前时间（可选，用于未来的时间导数计算）
        V_prev: 前一个二进制视觉投影场数组（可选）
        t_prev: 前一个时间（可选）

    可选参数:
        所有行为参数都可以选择性地传递给函数，
        如果要覆盖默认值，例如当检测到多个类别时
        GAM, V0, ALP0, ALP1, ALP2, BET0, BET1, BET2: 行为算法参数

    返回:
        tuple: (dvel, dpsi)
            dvel: 智能体速度的时间变化
            dpsi: 智能体航向角的时间变化

    算法说明:
        基于视觉投影场计算速度和转向的变化，
        实现避障、聚集、对齐等集群行为。
    """
    # # 时间导数计算（未来功能，当前已注释）
    # if V_prev is not None and t_prev is not None and t_now is not None:
    #     dt = t_now - t_prev
    #     logger.debug('Movement calculation called with NONE as time-related parameters.')
    #     joined_V = np.vstack((V_prev, t_prev))
    #     dt_V = dt_V_of(dt, joined_V)
    # else:
    #     dt_V = np.zeros(len(Phi))

    # 如果没有覆盖，获取行为参数
    if GAM is None:
        GAM = behavior.GAM      # 速度衰减系数
    if V0 is None:
        V0 = behavior.V0        # 基础速度
    if ALP0 is None:
        ALP0 = behavior.ALP0    # 速度响应基础增益
    if ALP1 is None:
        ALP1 = behavior.ALP1    # 速度响应线性增益
    if ALP2 is None:
        ALP2 = behavior.ALP2    # 速度响应二次增益
    if BET0 is None:
        BET0 = behavior.BET0    # 转向响应基础增益
    if BET1 is None:
        BET1 = behavior.BET1    # 转向响应线性增益
    if BET2 is None:
        BET2 = behavior.BET2    # 转向响应二次增益

    # 初始化时间导数为零（当前版本不使用时间导数）
    dt_V = np.zeros(len(Phi))

    # 计算VPF相对于Phi的导数
    dPhi_V = dPhi_V_of(Phi, V_now)

    # 计算函数G的级数展开
    # 速度相关的G函数
    G_vel = (-V_now + ALP2 * dt_V)

    # 尖峰部分需要单独处理，因为数值积分的原因
    G_vel_spike = np.square(dPhi_V)

    # 转向相关的G函数
    G_psi = (-V_now + BET2 * dt_V)

    # 尖峰部分需要单独处理，因为数值积分的原因
    G_psi_spike = np.square(dPhi_V)

    # 计算转向变化dpsi
    if not algoimp.WITH_SIGMOID_MASK_TURN:
        # 原始算法
        dpsi = BET0 * integrate.trapz(np.sin(Phi) * G_psi, Phi) + \
               BET0 * BET1 * np.sum(np.sin(Phi) * G_psi_spike)
    else:
        # S型掩码算法（实验性改进）
        dpsi = BET0 * integrate.trapz(sin_sigmoid(Phi, s=algoimp.SIGMOID_MASK_TURN_STEEP*np.pi) * G_psi, Phi) + \
               BET0 * BET1 * np.sum(sin_sigmoid(Phi, s=algoimp.SIGMOID_MASK_TURN_STEEP*np.pi) * G_psi_spike)

    # 计算速度变化dvel
    if not algoimp.WITH_SIGMOID_MASK_ACC:
        # 原始算法
        dvel = GAM * (V0 - vel_now) + \
               ALP0 * integrate.trapz(np.cos(Phi) * G_vel, Phi) + \
               ALP0 * ALP1 * np.sum(np.cos(Phi) * G_vel_spike)
    else:
        # S型掩码算法（实验性改进）
        dvel = GAM * (V0 - vel_now) + \
               ALP0 * integrate.trapz(cos_sigmoid(Phi, algoimp.SIGMOID_MASK_ACC_STEEP*np.pi) * G_vel, Phi) + \
               ALP0 * ALP1 * np.sum(cos_sigmoid(Phi, algoimp.SIGMOID_MASK_ACC_STEEP*np.pi) * G_vel_spike)

    return dvel, dpsi


def sigmoid(x, s):
    """
    具有陡峭度s的S型函数

    参数:
        x: 输入值
        s: 陡峭度参数

    返回:
        S型函数值，范围在[-1, 1]之间
    """
    return 2 / (1 + np.exp(-s*x)) - 1


def cos_sigmoid(x, s):
    """
    类似余弦函数的复合S型函数，具有陡峭度s

    用于算法改进中的S型掩码，替代标准的余弦函数。

    参数:
        x: 输入角度数组
        s: 陡峭度参数

    返回:
        类似余弦的S型函数值列表
    """
    # 左半部分
    left = 2 / (1 + np.exp(-s * (x + (np.pi / 2)))) - 1
    # 右半部分
    right = -2 / (1 + np.exp(-s * (x - (np.pi / 2)))) + 1
    final = []
    for xid, xi in enumerate(list(x)):
        if xi < 0:
            final.append(left[xid])
        else:
            final.append(right[xid])
    return final


def sin_sigmoid(x, s):
    """
    类似正弦函数的复合S型函数，具有陡峭度s

    用于算法改进中的S型掩码，替代标准的正弦函数。

    参数:
        x: 输入角度数组
        s: 陡峭度参数

    返回:
        类似正弦的S型函数值列表
    """
    # 中间部分
    middle = 2 / (1 + np.exp(-s * (x))) - 1
    # 左边部分
    left = -2 / (1 + np.exp(-s * (x + (np.pi)))) + 1
    # 右边部分
    right = -2 / (1 + np.exp(-s * (x - (np.pi)))) + 1
    final = []
    for xid, xi in enumerate(list(x)):
        if -np.pi / 2 < xi < np.pi / 2:
            final.append(middle[xid])
        elif xi < -np.pi / 2:
            final.append(left[xid])
        else:
            final.append(right[xid])
    return final