# -*- coding: utf-8 -*-
"""
VisualSwarm 行为控制模块
========================

该子模块实现了根据视觉流进行控制的核心逻辑。
将视觉投影场(VPF)转换为具体的运动控制命令。

主要功能:
- 从VPF计算状态变量（速度变化、转向变化）
- 实现集群行为算法的核心逻辑
- 处理算法改进和优化
- 生成电机控制命令

作者: mezdahun
描述: 根据视觉流实现控制的子模块
"""
import datetime
import logging

import numpy as np

import visualswarm.contrib.vision
from visualswarm.monitoring import ifdb
from visualswarm.contrib import monitoring, simulation, control
from visualswarm.contrib import algorithm_improvements as algoimp
from visualswarm.behavior import statevarcomp
from visualswarm import env
from queue import Empty

if monitoring.ENABLE_CLOUD_STORAGE:
    import pickle  # nosec

# 使用主日志器
if not simulation.ENABLE_SIMULATION:
    # 设置日志（真实机器人模式）
    import os
    ROBOT_NAME = os.getenv('ROBOT_NAME', 'Robot')
    logger = logging.getLogger(f'VSWRM|{ROBOT_NAME}')
    logger.setLevel(monitoring.LOG_LEVEL)
else:
    # 仿真模式日志器
    logger = logging.getLogger('visualswarm.app_simulation')  # pragma: simulation no cover

def get_latest_element(queue):  # pragma: simulation no cover
    """
    获取队列中的最新元素并清空FIFO队列对象

    用于慢进程消费由快进程填充的队列元素。
    这确保了总是处理最新的数据，避免延迟累积。

    参数:
        queue (multiprocessing.Queue): 要清空并返回最新元素的队列对象

    返回:
        val: 队列中的最新值
    """
    val = None
    while not queue.empty():
        try:
            val = queue.get_nowait()
        except Empty:
            return val
    return val

def VPF_to_behavior(VPF_stream, control_stream, motor_control_mode_stream, with_control=False):
    """
    从高级视觉输入中提取最终视觉投影场并转换为行为控制

    这是VisualSwarm系统的核心函数，实现了从视觉感知到运动控制的完整流程。

    参数:
        VPF_stream: 视觉投影场数据流
        control_stream: 控制命令输出流
        motor_control_mode_stream: 电机控制模式流
        with_control: 是否启用实际的电机控制

    功能:
        - 处理VPF数据
        - 计算状态变量（速度和转向变化）
        - 应用算法改进
        - 生成电机控制命令
        Args:
            VPF_stream (multiprocessing Queue): stream to receive visual projection field
            control_stream (multiprocessing Queue): stream to push calculated control parameters
            motor_control_mode_stream (multiprocessing Queue): stream to determine which movement regime the agent
                should follow
            with_control (boolean): if true, the output of the algorithm is sent to the movement processes.
        Returns:
            -shall not return-
    """
    try:
        if not simulation.ENABLE_SIMULATION:
            measurement_name = "control_parameters"
            ifclient = ifdb.create_ifclient()

        phi = None
        v = 0
        t_prev = datetime.datetime.now()
        is_initialized = False
        # start_behave = t_prev
        # prev_sign = 0

        element = None
        while element is None:
            element = get_latest_element(VPF_stream)

        (projection_field, capture_timestamp, projection_field_c2) = element

        if not visualswarm.contrib.vision.divided_projection_field:
            phi = np.linspace(visualswarm.contrib.vision.PHI_START, visualswarm.contrib.vision.PHI_END,
                              len(projection_field))
        else:
            phi = np.linspace(visualswarm.contrib.vision.PHI_START, visualswarm.contrib.vision.PHI_END,
                              projection_field.shape[0])

        ROBOT_NAME = os.getenv('ROBOT_NAME', 'Robot')
        EXP_ID = os.getenv('EXP_ID', 'expXXXXXX')
        statevar_timestamp = datetime.datetime.now().strftime("%d-%m-%y-%H%M%S")
        statevars_fpath = os.path.join(monitoring.SAVED_VIDEO_FOLDER, f'{statevar_timestamp}_{EXP_ID}_{ROBOT_NAME}_statevars.npy')
        if monitoring.ENABLE_CLOUD_STORAGE:
            os.makedirs(monitoring.SAVED_VIDEO_FOLDER, exist_ok=True)

        rw_dt = 0
        add_psi = 0.1
        new_dpsi = 0.05

        dpsi_before = None

        while True:
            try:
                # the possibility of 2 detection classes is already included, only using the first for now
                (projection_field, capture_timestamp, projection_field_c2) = get_latest_element(VPF_stream)
            except:
                continue

            if np.mean(projection_field) == 0:
                if control.EXP_MOVE_TYPE != 'NoExploration' or algoimp.WITH_EXPLORE_ROT or algoimp.WITH_EXPLORE_ROT_CONT:
                    movement_mode = "EXPLORE"
                else:
                    movement_mode = "BEHAVE"
            else:
                movement_mode = "BEHAVE"

            t_now = datetime.datetime.now()
            dt = (t_now - t_prev).total_seconds()  # to normalize

            if not visualswarm.contrib.vision.divided_projection_field:
                dv, dpsi = statevarcomp.compute_state_variables(v, phi, projection_field)
            else:
                dvs = []
                dpsis = []
                projection_field_orig = np.max(projection_field, axis=-1)
                dv_orig, dpsi_orig = statevarcomp.compute_state_variables(v, phi, projection_field_orig)
                for i in range(projection_field.shape[-1]):
                    dvi, dpsii = statevarcomp.compute_state_variables(v, phi, projection_field[:, i])
                    dvs.append(dvi)
                    dpsis.append(dpsii)
                dv = np.sum(dvs)
                dpsi = np.sum(dpsis)
                # print(f"According to original algorithm: dv={dv_orig}, dpsi={dpsi_orig}")
                # print(dvs, dpsis)
                # print(f"With Improved edge overlay (mean): dv={np.mean(dvs)}, dpsi={np.mean(dpsis)}")
                # print(f"With Improved edge overlay (sum): dv={np.sum(dvs)}, dpsi={np.sum(dpsis)}")

            if v > 0:
                v = min(v, 400)
            elif v < 0:
                v = max(v, -400)

            v = float(v)
            dpsi = float(dpsi)

            ## Smooth random walk if requested by user (temporary)
            if np.mean(projection_field) == 0 and control.SMOOTH_RW:
                if rw_dt > 2:
                    new_dpsi = np.random.uniform(-add_psi, add_psi, 1)
                    rw_dt = 0
                    # the more time spent without social cues the more extensive the exploration is
                    if add_psi < 1.5:
                        logger.error(f'add dpsi, {add_psi}')
                        add_psi += 0.1
                dpsi = new_dpsi
                rw_dt += dt
            else:
                # logger.error('zerodpsi')
                add_psi = 0.1

            # Initializing control parameters
            if is_initialized:
                v += dv * dt
                dpsi

            else:
                is_initialized = True
                dv = float(0)
                dpsi = float(0)

            # now_sign = np.sign(dv)
            # prev_sign = now_sign

            t_prev = t_now

            if monitoring.SAVE_CONTROL_PARAMS and not simulation.ENABLE_SIMULATION:

                # take a timestamp for this measurement
                time = datetime.datetime.utcnow()

                # generating data to dump in db
                field_dict = {"agent_velocity": dv,
                              "heading_angle": dpsi,
                              "processing_delay": (time - capture_timestamp).total_seconds()}

                # format the data as a single measurement for influx
                body = [
                    {
                        "measurement": measurement_name,
                        "time": time,
                        "fields": field_dict
                    }
                ]

                ifclient.write_points(body, time_precision='ms')

            if with_control:
                # Only used if improvement is requested
                # creating excitation of left vs right of the projection field
                exc_left = np.mean(projection_field[0:int(len(projection_field) / 2)])
                exc_right = np.mean(projection_field[int(len(projection_field) / 2):])
                # counting how many blobs are detected
                num_blobs = count_retinal_blobs(projection_field)
                # forwarding values for robot control
                control_stream.put((v, dpsi, exc_left, exc_right, num_blobs))
                motor_control_mode_stream.put(movement_mode)

            # saving monitoring values
            if monitoring.ENABLE_CLOUD_STORAGE:
                with open(statevars_fpath, 'ab') as sv_f:
                    statevars = np.concatenate((np.array([t_now]),
                                                np.array([dv, dpsi])))
                    pickle.dump(statevars, sv_f)

            # To test infinite loops
            if env.EXIT_CONDITION:
                break

    except KeyboardInterrupt:
        pass


def count_retinal_blobs(projection_field):
    """Counting retinal blobs on a 1D binary projection field according to edge data (switching from 0 to 1, i.e. +1 or
    from +1 to 0 i.e. -1)."""
    # count positive edges of the vector
    pos_edges = np.sum(np.diff(projection_field) > 0)
    # count negative edges of the vector
    neg_edges = np.sum(np.diff(projection_field) < 0)
    print(f"found {pos_edges} positive edges and {neg_edges} negative edges")
    # count edges of the vector
    num_blobs = int(np.ceil(max(pos_edges, neg_edges)))
    return num_blobs
