# -*- coding: utf-8 -*-
"""
VisualSwarm 主控节点应用程序
============================

该模块实现了主控节点（Puppetmaster）功能，用于通过SSH远程控制和管理
整个机器人群体。主控节点可以同时启动、停止和监控多个机器人。

主要功能:
- 远程安装和更新机器人软件依赖
- 批量启动和停止机器人群体
- 分布式实验管理
- 网络摄像头控制

作者: mezdahun
描述: 主控节点应用程序，通过SSH管理机器人群体
"""

from fabric import ThreadingGroup as Group

from visualswarm.contrib import puppetmaster
from getpass import getpass

import os
import logging

# 设置日志
# logging.basicConfig()
logger = logging.getLogger(__name__)
logger.setLevel('INFO')

# 获取主控节点密码（用于SSH连接）
PSWD = getpass('Puppetmaster password: ')


def reinstall_robots():
    """
    从头重新安装机器人的依赖项（当出现问题时使用）

    注意: 这个过程需要较长时间，因为会完全重建虚拟环境。
    """
    logger.info("Reinstalling robots' virtual environment...")
    # 创建机器人群体连接组
    swarm = Group(*list(puppetmaster.HOSTS.values()), user=puppetmaster.UNAME)

    for c in swarm:
        c.connect_kwargs.password = PSWD
        # 执行完整的重新安装流程
        result = c.run(f'cd {puppetmaster.INSTALL_DIR} && '
                       'git pull && '                    # 拉取最新代码
                       'pipenv --rm && '                 # 删除现有虚拟环境
                       'pipenv install -d --skip-lock -e .',  # 重新安装依赖
                       hide=True,
                       pty=False)
        print(result.stdout)


def update_robots():
    """
    更新机器人的依赖项（当使用新包时）

    这是一个轻量级的更新过程，只更新代码和依赖，不重建环境。
    """
    logger.info("Updating robots' virtual environment...")
    # 创建机器人群体连接组
    swarm = Group(*list(puppetmaster.HOSTS.values()), user=puppetmaster.UNAME)

    for c in swarm:
        try:
            c.connect_kwargs.password = PSWD
            # 执行更新流程
            result = c.run(f'cd {puppetmaster.INSTALL_DIR} && '
                           'git pull && '                # 拉取最新代码
                           'pipenv install -d -e .',     # 更新依赖
                           pty=False)
            print(result.stdout)
        except Exception as e:
            logger.error(f'Error during updating robot: {c}')
            logger.error(f'Error: {e}')


def vswrm_start(c, robot_name):
    """
    在单个机器人/连接上启动VSWRM应用程序

    参数:
        c: Fabric连接对象
        robot_name: 机器人名称
    """
    # 从环境变量获取实验ID
    EXP_ID = os.getenv('EXP_ID', 'noexpid')
    c.connect_kwargs.password = PSWD

    # 在远程机器人上执行启动命令，包含所有必要的环境变量
    c.run(f'cd {puppetmaster.INSTALL_DIR} && '
          'git pull && '  # 拉取最新代码
          f'ENABLE_CLOUD_STORAGE=1 SAVE_VISION_VIDEO=0 SHOW_VISION_STREAMS=1 '  # 云存储和视觉设置
          f'SAVE_CNN_TRAINING_DATA=1 '  # 保存CNN训练数据
          f'RES_WIDTH=320 RES_HEIGHT=200 '  # 摄像头分辨率
          f'ROBOT_NAME={robot_name} EXP_ID={EXP_ID} LOG_LEVEL=DEBUG FLIP_CAMERA=0 ROBOT_FOV=8.5 '  # 机器人配置
          f'BET0=8 ALP0=60 V0=80 ALP1=0.0014 BET1=0.0014 '  # 行为算法参数
          'dtach -n /tmp/tmpdtach '  # 使用dtach在后台运行
          'pipenv run vswrm-start')  # 启动VisualSwarm


def vswrm_start_webcam(c, camera_name):
    """
    在给定摄像头IP上启动简单网络摄像头

    参数:
        c: Fabric连接对象
        camera_name: 摄像头名称（未使用但保留接口一致性）
    """
    c.connect_kwargs.password = PSWD
    c.run(f'cd {puppetmaster.INSTALL_DIR} && '
          'git pull && '  # 拉取最新代码
          'dtach -n /tmp/tmpdtach '  # 使用dtach在后台运行
          'pipenv run vswrm-start-webcam')  # 启动网络摄像头


def vswrm_stop_webcam(c):
    """
    在给定摄像头IP上停止简单网络摄像头

    参数:
        c: Fabric连接对象
    """
    c.connect_kwargs.password = PSWD
    # 查找网络摄像头进程
    start_result = c.run('ps -x  | grep "/bin/vswrm-start-webcam"')
    PID = start_result.stdout.split()[0]  # 获取vswrm第一个子进程的PID
    print(PID)
    # 向任何子进程发送INT信号将触发优雅退出（等同于KeyboardInterrupt）
    c.run(f'cd {puppetmaster.INSTALL_DIR} && kill -INT {int(PID)}')


def vswrm_stop(c):
    """
    在单个机器人/连接上停止VSWRM应用程序

    参数:
        c: Fabric连接对象
    """
    c.connect_kwargs.password = PSWD
    # 查找VSWRM进程
    start_result = c.run('ps -x  | grep "/bin/vswrm-start"')
    PID = start_result.stdout.split()[0]  # 获取vswrm第一个子进程的PID
    print(PID)
    # 向任何子进程发送INT信号将触发优雅退出（等同于KeyboardInterrupt）
    c.run(
        f'cd {puppetmaster.INSTALL_DIR} && touch release.txt && sleep 2 && kill -INT {int(PID)} && rm -rf release.txt')


def start_swarm():
    """
    在contrib.puppetmaster中定义的HOSTS机器人群体上启动VSWRM应用程序

    这个函数会同时启动所有配置的机器人，实现分布式集群实验。
    """
    logger.info('Puppetmaster started!')
    # 创建机器人群体连接组
    swarm = Group(*list(puppetmaster.HOSTS.values()), user=puppetmaster.UNAME)
    print(swarm)

    # 为每个连接启动VSWRM
    for connection in swarm:
        # 根据IP地址反向查找机器人名称
        robot_name = list(puppetmaster.HOSTS.keys())[list(puppetmaster.HOSTS.values()).index(connection.host)]
        print(f'Start VSWRM on {robot_name} with host {connection.host}')
        try:
            vswrm_start(connection, robot_name)
        except Exception as e:
            logger.error(f'Could not start VSWRM on robot: {connection}')
            logger.error(f'Error: {e}')

    # 等待用户输入以停止群体
    getpass('VSWRM started on swarm. Press any key to stop the swarm')

    # 停止所有机器人上的VSWRM进程
    logger.info('Killing VSWRM processes by collected PIDs...')
    for connection in swarm:
        robot_name = list(puppetmaster.HOSTS.keys())[list(puppetmaster.HOSTS.values()).index(connection.host)]
        logger.info(f'Stop VSWRM on {robot_name} with host {connection.host}')
        try:
            vswrm_stop(connection)
        except Exception as e:
            logger.error(f'Could not stop VSWRM on robot: {connection}')
            logger.error(f'Error: {e}')

def start_webcams():
    """
    启动contrib.puppetmaster中提供的所有可用网络摄像头

    这个函数用于启动用于监控和观察的外部摄像头设备。
    """
    logger.info('Puppetmaster webcam started!')
    # 创建网络摄像头连接组
    webcams = Group(*list(puppetmaster.WEBCAM_HOSTS.values()), user=puppetmaster.UNAME)
    print(webcams)

    # 启动所有网络摄像头
    for connection in webcams:
        # 根据IP地址反向查找摄像头名称
        wc_name = list(puppetmaster.WEBCAM_HOSTS.keys())[list(puppetmaster.WEBCAM_HOSTS.values()).index(connection.host)]
        print(f'Start VSWRM-webcam on {wc_name} with host {connection.host}')
        try:
            vswrm_start_webcam(connection, wc_name)
        except Exception as e:
            logger.error(f'Could not start VSWRM-webcam on webcam: {connection}')
            logger.error(f'Error: {e}')

    # 等待用户输入以停止摄像头
    getpass('VSWRM-webcam started on webcams. Press any key to stop them...')

    # 停止所有网络摄像头进程
    logger.info('Killing VSWRM-webcam processes by collected PIDs...')
    for connection in webcams:
        wc_name = list(puppetmaster.WEBCAM_HOSTS.keys())[list(puppetmaster.WEBCAM_HOSTS.values()).index(connection.host)]
        logger.info(f'Stop VSWRM-webcam on {wc_name} with host {connection.host}')
        try:
            vswrm_stop_webcam(connection)
        except Exception as e:
            logger.error(f'Could not stop VSWRM on robot: {connection}')
            logger.error(f'Error: {e}')

def shutdown_swarm(shutdown='shutdown'):
    """
    关闭/重启contrib.puppetmaster中定义的HOSTS机器人群体

    参数:
        shutdown (str): 关闭命令，'shutdown'表示关机，'reboot'表示重启
    """
    # 创建机器人群体连接组
    swarm = Group(*list(puppetmaster.HOSTS.values()), user=puppetmaster.UNAME)

    for connection in swarm:
        try:
            connection.connect_kwargs.password = PSWD
            logger.info(f'Shutdown robot with IP {connection.host}')
            # 执行关机或重启命令（需要sudo权限）
            connection.sudo(f'{shutdown} -h now')
        except Exception as e:
            logger.error(f'Could not stop VSWRM on robot: {connection}')
            logger.error(f'Error: {e}')


def shutdown_robots():
    """
    入口点：关闭contrib.puppetmaster中定义的HOSTS机器人群体

    这是一个便捷函数，用于批量关闭所有配置的机器人。
    """
    shutdown_swarm()


def restart_robots():
    """
    入口点：重启contrib.puppetmaster中定义的HOSTS机器人群体

    这是一个便捷函数，用于批量重启所有配置的机器人。
    """
    shutdown_swarm(shutdown='reboot')
