# -*- coding: utf-8 -*-
"""
环境变量配置模块
================

该模块读取环境变量并存储全局项目参数。
包括日志级别、InfluxDB配置和测试参数等。

作者: mezdahun
描述: 读取环境变量并存储全局项目参数
"""

import logging
import os

# 日志级别配置
LOG_LEVEL = logging.getLevelName(os.getenv('LOG_LEVEL', 'DEBUG'))  # 从环境变量获取日志级别，默认为DEBUG

# InfluxDB配置（从.env.local或默认值）
INFLUX_FRESH_DB_UPON_START = os.getenv('INFLUX_FRESH_DB_START', False)  # 启动时是否创建新数据库
INFLUX_USER = os.getenv('INFLUX_USER', 'grafana')                       # InfluxDB用户名
INFLUX_PSWD = os.getenv('INFLUX_PSWD')                                  # InfluxDB密码
INFLUX_DB_NAME = os.getenv('INFLUX_DB_NAME', 'home')                    # InfluxDB数据库名
INFLUX_HOST = os.getenv('INFLUX_HOST', '127.0.0.1')                     # InfluxDB主机地址
INFLUX_PORT = os.getenv('INFLUX_HOST', '8086')                          # InfluxDB端口号

# 测试参数
EXIT_CONDITION = False  # 退出条件标志，用于测试和调试
