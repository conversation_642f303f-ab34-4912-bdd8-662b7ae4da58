# -*- coding: utf-8 -*-
"""
VisualSwarm 仿真应用程序
========================

该模块实现了VisualSwarm在WeBots仿真环境中的运行。
提供了与WeBots的接口，包括摄像头图像获取、电机控制、传感器读取等功能。

主要功能:
- WeBots仿真环境接口
- 虚拟摄像头图像处理
- 仿真电机控制
- 传感器数据获取
- 仿真数据记录和分析

作者: mezdahun
描述: VisualSwarm仿真应用程序，在WeBots环境中运行
"""

import json
import logging
import numpy as np
# pickle仅用于写入数据，nosec
import pickle  # nosec
from contextlib import ExitStack

from visualswarm import env
from visualswarm.contrib import logparams, simulation, control, behavior, vision
from visualswarm.simulation_tools import processing_tools, webots_tools

from freezegun import freeze_time
import datetime
import time

# 设置日志
logging.basicConfig()
logger = logging.getLogger(__name__)
logger.setLevel(env.LOG_LEVEL)
bcolors = logparams.BColors


def webots_do(control_args, devices):
    """
    WeBots控制接口

    参数:
        control_args: 控制命令和参数的元组
        devices: WeBots设备字典

    功能:
        - 处理电机控制命令
        - 处理LED灯控制命令
    """
    command = control_args[0]      # 命令类型
    command_arg = control_args[1]  # 命令参数

    if command == "SET_MOTOR":
        # 将Thymio电机速度转换为WeBots电机速度
        v_left = command_arg['left'] * (simulation.MAX_WEBOTS_MOTOR_SPEED / control.MAX_MOTOR_SPEED)
        v_right = command_arg['right'] * (simulation.MAX_WEBOTS_MOTOR_SPEED / control.MAX_MOTOR_SPEED)
        logger.debug(f"webots_do move: left {v_left}, right {v_right}")
        # 设置左右电机速度
        devices['motors']['left'].setVelocity(v_left)
        devices['motors']['right'].setVelocity(v_right)
    elif command == "LIGHTUP_LED":
        logger.debug(f"webots_do light: color {command_arg}")
        # 设置LED灯颜色
        devices['leds']['top'].set(command_arg)


def getWebotsCameraImage(devices):
    """
    从WeBots摄像头对象快速获取摄像头图像

    参数:
        devices: WeBots设备字典

    返回:
        numpy.ndarray: 处理后的图像数组

    功能:
        - 获取原始摄像头数据
        - 根据视野角度裁剪图像
        - 转换为numpy数组格式
    """
    width = devices['params']['c_width']    # 摄像头宽度
    height = devices['params']['c_height']  # 摄像头高度

    # 从WeBots摄像头获取原始图像数据
    camera_data = devices['camera'].getImage()
    # 将原始数据转换为numpy数组并重塑为图像格式
    img = np.frombuffer(camera_data, np.uint8).reshape((height, width, 4))

    # 根据视野角度计算需要使用的像素宽度
    use_pixel_w = int(width * (vision.FOV / 6.28))
    w_start = int((width - use_pixel_w) / 2)  # 裁剪起始位置
    w_end = w_start + use_pixel_w             # 裁剪结束位置

    # 返回裁剪后的图像
    return img[:, w_start:w_end, :]


def webots_entrypoint(robot, devices, timestep, with_control=False):
    """
    WeBots主应用程序入口点

    参数:
        robot: WeBots机器人对象
        devices: WeBots设备字典
        timestep: 仿真时间步长
        with_control: 是否启用电机控制

    功能:
        - 初始化仿真环境
        - 启动所有子进程
        - 运行主仿真循环
        - 处理传感器数据和摄像头图像
        - 保存仿真数据

    注意: 该入口点已包含主仿真循环，从WeBots控制器脚本调用。
    """
    logger.info(f'Started VSWRM-Webots interface app with timestep: {timestep}')

    # 设置仿真开始时间（冻结时间用于可重复的实验）
    simulation_start_time = '2000-01-01 12:00:01'
    logger.info(f'Freezing time to: {simulation_start_time}')

    with freeze_time(simulation_start_time) as freezer:

        # 创建子进程间共享的传感器和电机值队列
        streams = processing_tools.return_processing_streams()
        # 创建子进程
        processes = processing_tools.return_processes(streams, with_control=with_control)
        # 获取需要从主线程填充的流
        [sensor_stream, _, webots_do_stream, raw_vision_stream, _, _, _, _, _, _, _] = streams

        # 启动所有子进程
        processing_tools.start_processes(processes)

        # 使用计数器和仿真时间控制给定动作的频率
        sensor_get_time = 0                                      # 传感器获取时间计数器
        camera_sampling_period = devices['camera'].getSamplingPeriod()  # 摄像头采样周期
        camera_get_time = 0                                      # 摄像头获取时间计数器
        frame_id = 0                                             # 帧ID计数器
        simulation_time = 0                                      # 跟踪仿真中的虚拟时间（毫秒）

        logger.info(f'{bcolors.OKGREEN}START{bcolors.ENDC} raw vision (webots) from main thread/process')

        # 性能监控变量
        avg_times = np.zeros(5)      # 平均时间数组
        timer_counts = np.zeros(5)   # 计时器计数数组
        t_end = time.perf_counter()  # 上一次循环结束时间

        # 如果需要保存仿真数据，获取文件名并创建文件夹
        if simulation.WEBOTS_SAVE_SIMULATION_DATA:
            file_paths = processing_tools.assure_data_folders(robot.getName())
            position_fpath, orientation_fpath, params_fpath, run_number = file_paths

        # 使用上下文管理器处理文件操作（如果不保存数据则使用ExitStack）
        with ExitStack() if not simulation.WEBOTS_SAVE_SIMULATION_DATA else open(position_fpath, 'ab') as pos_f:
            with ExitStack() if not simulation.WEBOTS_SAVE_SIMULATION_DATA else open(orientation_fpath, 'ab') as or_f:
                # 主仿真循环
                while robot.step(timestep) != -1:

                    # 性能监控：记录循环开始时间
                    t_start = time.perf_counter()
                    dt_tick = t_start - t_end
                    avg_times[4] += dt_tick
                    timer_counts[4] += 1

                    t0 = time.perf_counter()

                    # 如果请求，保存仿真数据
                    if simulation.WEBOTS_SAVE_SIMULATION_DATA:
                        raw_or_vals = devices['monitor']['orientation'].getValues()
                        r_orientation = np.concatenate((np.array([simulation_time]),
                                                        np.array([processing_tools.robot_orientation(raw_or_vals)])))
                        r_position = np.concatenate((np.array([simulation_time]),
                                                     np.array(devices['monitor']['gps'].getValues())))

                        pickle.dump(r_position, pos_f)
                        pickle.dump(r_orientation, or_f)

                        if control.BORDER_CONDITION == "Infinite":
                            webots_tools.teleport_to_center_if_needed(robot, r_position)

                    t1 = time.perf_counter()
                    dt_save = t1 - t0
                    avg_times[0] += dt_save
                    timer_counts[0] += 1

                    # Fetching camera image on a predefined frequency
                    if camera_get_time > camera_sampling_period:
                        t0 = time.perf_counter()

                        try:
                            raw_vision_stream.get_nowait()
                        except:
                            logger.debug("Exception duing fetching from raw camera stream!")

                        img = getWebotsCameraImage(devices)
                        raw_vision_stream.put((img, frame_id, datetime.datetime.utcnow()))

                        frame_id += 1
                        camera_get_time = camera_get_time % (1 / simulation.UPFREQ_PROX_HORIZONTAL)

                        t1 = time.perf_counter()
                        dt_image = t1 - t0
                        avg_times[1] += dt_image
                        timer_counts[1] += 1

                    # Thymio updates sensor values on predefined frequency
                    if (sensor_get_time / 1000) > (1 / simulation.UPFREQ_PROX_HORIZONTAL):
                        t0 = time.perf_counter()

                        prox_vals = [i.getValue() for i in devices['sensors']['prox']['horizontal']]
                        try:
                            sensor_stream.get_nowait()
                        except:
                            logger.debug("Exception duing fetching from sensory stream!")
                        sensor_stream.put(prox_vals)
                        if np.any(np.array(prox_vals[0:5]) > control.EMERGENCY_PROX_THRESHOLD) and \
                                simulation.WEBOTS_SAVE_SIMULATION_DATA:
                            webots_tools.write_ER_timestamp(robot.getName(), params_fpath, run_number, simulation_time)
                        sensor_get_time = sensor_get_time % (1 / simulation.UPFREQ_PROX_HORIZONTAL)

                        t1 = time.perf_counter()
                        dt_prox = t1 - t0
                        avg_times[2] += dt_prox
                        timer_counts[2] += 1

                    # Acting on robot devices according to controller
                    if webots_do_stream.qsize() > 0:
                        t0 = time.perf_counter()

                        command_set = webots_do_stream.get_nowait()
                        logger.debug(command_set)
                        webots_do(command_set, devices)

                        t1 = time.perf_counter()
                        dt_webotsdo = t1 - t0
                        avg_times[3] += dt_webotsdo
                        timer_counts[3] += 1

                    # increment virtual time counters
                    sensor_get_time += timestep
                    camera_get_time += timestep
                    simulation_time += timestep

                    if simulation.PAUSE_SIMULATION_AFTER > 0:
                        if simulation_time > simulation.PAUSE_SIMULATION_AFTER * 1000:
                            # saving algorithm parameters into json file if requested
                            with ExitStack() if not simulation.WEBOTS_SAVE_SIMULATION_DATA else open(params_fpath,
                                                                                                     'w') as param_f:
                                logger.info('SAVING DATA')
                                json.dump(behavior.get_params(), param_f, indent=4)

                            if simulation.WEBOTS_SAVE_SIMULATION_VIDEO:
                                # finishing and saving video, all robots shall wait with termination until finished
                                if robot.getName() == "robot0":
                                    # dedicated recorder robot stops video
                                    robot.movieStopRecording()
                                while not robot.movieIsReady():
                                    time.sleep(0.5)
                                    logger.info('Waiting for video to be ready before end simulation...')

                            # termination of simulation (either pause or quit)
                            if simulation.PAUSE_BEHAVIOR == "Quit":
                                robot.simulationQuit(0)
                            elif simulation.PAUSE_BEHAVIOR == "Pause":
                                robot.simulationSetMode(robot.SIMULATION_MODE_PAUSE)

                    # ticking virtual time with virtual time of Webots environment
                    freezer.tick(delta=datetime.timedelta(milliseconds=timestep))

                    t_end = time.perf_counter()

                    if simulation.WEBOTS_LOG_PERFORMANCE:
                        used_times = avg_times / timer_counts * 1000
                        logger.info(f'\nAVG Used times: \n'
                                    f'\t---data save: {used_times[0]} \n'
                                    f'\t---image passing: {used_times[1]}\n'
                                    f'\t---sensor passing: {used_times[2]}\n'
                                    f'\t---set devices: {used_times[3]}\n'
                                    f'\t---step world physics: {used_times[4]}\n')

        processing_tools.stop_and_cleanup(processes, streams)
