<!DOCTYPE aseba-playground>
<!-- Simple simulation environment with 1 robot -->
<!-- github: https://github.com/mezdahun/VisualSwarm -->
<!-- author: mezdahun -->
<!--version: Aseba v1.6+ required for simulation-->

<!--Beginning of Playground definition-->
<aseba-playground>
	<author name="mezdahun" />
	<description lang="en">Simple simulation environment for VisualSwarm project.</description>

<!--	COLOR: Colors can be defined by their r,g,b components (each from 0 to 1) and given a name for later use.-->

	<color name="white" r="1.0" g="1.0" b="1.0" />
    <color name="base white" r="0.95" g="0.94" b="0.92" />
	<color name="yellow" r="1.0" g="1.0" b="0" />
	<color name="red" r="1.0" g="0" b="0" />
	<color name="blue" r="0" g="0" b="1.0" />


<!--	WORLD: width (w) and height (h) in cm (origin in a corner). Can also have a color -->
<!--    (defined above) and ground texture (PNG image). The PNG image will be stretched to the world's size. -->
<!--    Attribute energyScoringSystemEnabled defines whether the energy of the e-pucks will be counted.-->

	<world w="500" h="500" color="base white" energyScoringSystemEnabled="false"/>


<!--	CAMERA: The position of the camera can be defined by its x,y and altitude in cm, yaw, pitch in radians.-->

<!--	<camera x="250" y="250" altitude="130" yaw=" 1.57" pitch="-1.56" />-->


<!--    WALLS: are defined by their center coordinates x, y, dimensions l1, l2, height h in cm and angle in radians. -->
<!--    A mass m can also be given, if m is not defined it becomes -1 which means unmovable. If m>1, the object -->
<!--    can be moved if sufficient force is applied. Please note however that the simulator is -->
<!--    very basic so the results can look inaccurate.-->

<!--	<wall x="40.00" y="60.00" l1="15" l2="2.00" h="10.00" color="red" angle="-2.356" />-->


<!--	ROBOTS: Thymio II and E-Puck are available. The simulated E-Puck's API is different from the real E-Puck's -->
<!--    API, and they have a feeding system. The simulated Thymio's API is the same as the real Thymio's API -->
<!--    (no feeding system) but some functions, events, variables, while available, are not simulated. -->
<!--    Attributes are the robot's center position in the world, x and y, in cm, its anglein radians, -->
<!--    port number and node ID.-->
	<robot type="thymio2" x="250" y="250" port="33334" angle="-1.57" name="Roger" nodeId="1"/>

<!--	External processes can be run, here for example to load an aesl file (the basic behaviours of Thymio) on a robot.-->
<!--	<process command=":asebamassloader &#45;&#45;once thymio-default-behaviours.aesl tcp:localhost;33334" />-->

<!--End of Playground definition-->
</aseba-playground>